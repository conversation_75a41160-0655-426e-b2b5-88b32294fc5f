"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";

import { AddtoOrderState, UpdateOrderStripeDetails } from "@/services/ordersServices";
import { Timestamp } from "firebase/firestore";
import { getProfileNameByUserId } from "@/services/usersServices";

import { useRouter } from "next/navigation";
import { Card, CardBody } from "@heroui/react";
import { getLoggedInUserId } from "@/lib/utils";

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id");
  const isInvoice = searchParams.get("isInvoice");
  const formateDate = searchParams.get("formateDate");

  const transactionId = searchParams.get("transaction_id");

  // New flow: Handle direct payment intent success
  const paymentIntentId = searchParams.get("payment_intent");
  const redirectStatus = searchParams.get("redirect_status");

  // Debug: Log all URL parameters
  // console.log("🔍 URL Parameters:", {
  //   paymentIntentId,
  //   redirectStatus,
  //   allParams: Object.fromEntries(searchParams.entries()),
  // });

  // If this is our new flow (payment intent + redirect status), handle it
  useEffect(() => {
    if (
      paymentIntentId &&
      (redirectStatus === "succeeded" || redirectStatus === "requires_capture")
    ) {
      // console.log("🎉 New payment flow detected, processing...", {
      //   paymentIntentId,
      //   redirectStatus,
      // });

      // Extract order info from URL parameters first, then localStorage as fallback
      const urlOrderId = searchParams.get("order_id");
      const urlTransactionId = searchParams.get("transaction_id");
      const urlUserId = searchParams.get("user_id");
      const urlSellerId = searchParams.get("seller_id");
      const urlAmount = searchParams.get("amount");
      const urlCurrency = searchParams.get("currency");
      const urlIsEscrow = searchParams.get("is_escrow") === "true";

      let orderData = null;

      // Try URL parameters first
      if (urlOrderId && urlUserId && urlSellerId && urlAmount && urlCurrency) {
        orderData = {
          orderId: urlOrderId,
          transactionId: urlTransactionId,
          userId: urlUserId,
          sellerId: urlSellerId,
          amount: parseInt(urlAmount),
          currency: urlCurrency,
          isEscrow: urlIsEscrow,
          userEmail: "",
          userName: "Customer",
        };
      } else {
        // Fallback to localStorage
        const storedOrderData = localStorage.getItem("currentOrderData");
        if (storedOrderData) {
          try {
            orderData = JSON.parse(storedOrderData);
          } catch (error) {
            console.error("Error parsing stored order data:", error);
          }
        }
      }

      if (orderData) {
        // console.log("📤 Calling payment success API with data:", orderData);
        // Call our payment success API
        fetch("/api/payment/success", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            paymentIntentId: paymentIntentId,
            orderId: orderData.orderId,
            transactionId: orderData.transactionId,
            amount: orderData.amount,
            currency: orderData.currency,
            isEscrow: orderData.isEscrow,
            userId: orderData.userId,
            sellerId: orderData.sellerId,
            userEmail: orderData.userEmail,
            userName: orderData.userName,
          }),
        })
          .then((response) => response.json())
          .then((result) => {
            if (result.success) {
              console.log("✅ Payment processing completed");
              // Clear stored data
              localStorage.removeItem("currentOrderData");
              // Redirect to orders
              window.location.href = `/orders?highlight=${orderData.orderId}`;
            } else {
              console.error("❌ Payment processing failed:", result.error);
              // Continue with existing flow
            }
          })
          .catch((error) => {
            console.error("❌ Error calling payment success API:", error);
            // Continue with existing flow
          });
      } else {
        console.log("❌ No order data available for processing");
      }
    }
  }, [paymentIntentId, redirectStatus, searchParams]);

  const [loading, setLoading] = useState(true);
  const [orderStateUpdated, setOrderStateUpdated] = useState(false);
  const router = useRouter();

  // Function to update order state after successful payment
  const updateOrderState = async (transaction: {
    orderId: string;
    userId: string;
    sellerId: string;
  }) => {
    if (orderStateUpdated || !transaction.orderId) {
      return; // Already updated or no order ID
    }

    try {
      console.log("🔄 Updating order state for successful payment...");

      // Get profile names for buyer and seller
      const [buyerProfileName, sellerProfileName] = await Promise.all([
        getProfileNameByUserId(transaction.userId),
        transaction.sellerId ? getProfileNameByUserId(transaction.sellerId) : null,
      ]);
      console.log("buyerProfileName", buyerProfileName);
      console.log("sellerProfileName", sellerProfileName);

      if (!buyerProfileName) {
        console.warn("⚠️ Could not fetch buyer profile name");
        return;
      }

      if (!sellerProfileName && transaction.sellerId) {
        console.warn("⚠️ Could not fetch seller profile name");
        return;
      }
      await AddtoOrderState({
        id: transaction.orderId,
        loggedInUser: buyerProfileName,
        sellerName: sellerProfileName || "Unknown Seller",
        userName: buyerProfileName,
        sendInvoice: isInvoice === "true",
        dueDate: formateDate ? Timestamp.fromDate(new Date(formateDate)) : undefined,
      });

      // console.log("✅ Order state updated successfully", respon);

      console.log("✅ Order state updated successfully");

      setOrderStateUpdated(true);
      console.log("✅ Order state updated successfully");
    } catch (error) {
      console.error("❌ Error updating order state:", error);
    }
  };

  useEffect(() => {
   
    const fetchTransactionDetails = async () => {
 console.log("🔍 URL Parameters:", {
      paymentIntentId,
      redirectStatus,
      allParams: Object.fromEntries(searchParams.entries()),
    });

    console.log({
      orderId: searchParams.get("order_id") ?? "",
      userId: (await getLoggedInUserId() )?? "",
      sellerId: searchParams.get("sellerId") ?? "",
    },{orderStateUpdated});
    

    updateOrderState({
      orderId: searchParams.get("order_id") ?? "",
      userId: (await getLoggedInUserId() )?? "",
      sellerId: searchParams.get("sellerId") ?? "",
    });      
      
      
      let transactionId = searchParams.get("transactionId");
      // console.log("🚀",transactionId);
      // alert(transactionId);

      // if (!transactionId) {
      //   setError("No transaction information found");
      //   setLoading(false);
      //   return;
      // }

      try {
        // Immediately try to store payment details if we have transaction ID
        if (transactionId) {
          console.log(
            "🔄 Immediately attempting to store payment details for transaction:",
            transactionId
          );
          let txn_response = await fetch(`/api/payment-intent/${paymentIntentId}`);
          const txn_result = await txn_response.json();

          await UpdateOrderStripeDetails({
            id: searchParams.get("order_id") ?? "",
            chargeId: txn_result?.charge ?? "",
            transactionId: paymentIntentId ?? "",
          });
        }
      } catch (err) {
        console.error("Error fetching transaction details:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactionDetails();
  }, [sessionId, transactionId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transaction details...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-[calc(100vh-205px)] max-w-xl flex items-center justify-center mx-auto p-4">
        <Card>
          <CardBody>
            <div className="text-center p-3 ">
              <p>
                {isInvoice
                  ? "Your invoice has been created and sent to your email address."
                  : "Your payment has been received."}
              </p>

              <p>{formateDate}</p>

              <div className="flex justify-center items-center mb-4">
                <img src="/assets/check.svg" alt="" />
              </div>
              <p className="mb-6">
                The amount has been pre-authorized Your order will be confirmed by the service
                provider within 48 hour.
              </p>

              <div
                className="rounded-full w-full mt-5 cursor-pointer  border-2 py-5 text-base btn btn-sm "
                onClick={() => {
                  router.push(`${window.location.pathname}?sidebar=orders`, {
                    scroll: false,
                  });
                  window.dispatchEvent(new Event("openSidebar"));
                }}
              >
                Go to Order
              </div>
              <div
                className="rounded-full w-full mt-3 cursor-pointer  border-2 py-5 text-base btn btn-sm "
                onClick={() => {
                  router.push(`/`);
                }}
              >
                Home
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </>
  );
}

export default function PaymentSuccess() {
  return (
    <Suspense
      fallback={
        <div className="min-h-[calc(100vh-205px)] flex items-center justify-center ">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <PaymentSuccessContent />
    </Suspense>
  );
}
