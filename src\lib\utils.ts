import { clsx, type ClassValue } from "clsx";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { twMerge } from "tailwind-merge";
import { format } from "date-fns";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export function getLocation(
  attributes: { type: string; key: string; value: string }[] | undefined
): string | undefined {
  const locationAttr = attributes?.find((attr) => attr.key === "location");
  return locationAttr?.value;
}

export function getFormattedTime(isoDate: string): string {
  const date = new Date(isoDate);
  const day = date.getUTCDate();
  const month = date.toLocaleString("default", { month: "long", timeZone: "UTC" });
  const year = date.getUTCFullYear();

  const formatted = `${day} ${month} ${year}`;
  return formatted;
}

export async function getIdToken() {
   const auth = getAuth();
     return new Promise((resolve, reject) => {
    onAuthStateChanged(auth, async(user) => {
      if (user) {
         const idToken = await user.getIdToken();
        resolve(idToken);
      } else {
        reject(new Error("User not logged in"));
      }
    });
  });
  //  const user = auth.currentUser;

  // if (!user) {
  //   throw new Error("User not logged in");
  // }
  // const idToken = await user.getIdToken();
  // return idToken
}



export const BASE_URL = process.env.PROJECT_ENV === "production" ? 
'https://www.amuzn.app' : 
process.env.PROJECT_ENV === "development" ? 
 "https://amuzn-webapp-dev.vercel.app" : "http://127.0.0.1:3000";
 
//export const BASE_URL ='https://www.amuzn.app/'; // PROD
export const GATE_URL = `/api/gate`;

export async function getLoggedInUserId() {
   const auth = getAuth();
     return new Promise((resolve, reject) => {
    onAuthStateChanged(auth, (user) => {
      if (user) {
        resolve(user.uid);
      } else {
        reject(new Error("User not logged in"));
      }
    });
  });
  //  const user = auth.currentUser;
   
  //  console.log({user , auth,check:auth?.currentUser,nnn:auth.name});
   

  // if (!user) {
  //   throw new Error("User not logged in");
  // }
  // return user?.uid
}

  export const formatDate = (dateInput: any) => {
    let date: Date;
    if (
      dateInput &&
      typeof dateInput === "object" &&
      "_seconds" in dateInput &&
      "_nanoseconds" in dateInput
    ) {
      // Firestore Timestamp
      date = new Date(dateInput._seconds * 1000 + dateInput._nanoseconds / 1e6);
    } else if(
 dateInput &&
      typeof dateInput === "object" &&
      "seconds" in dateInput &&
      "nanoseconds" in dateInput      
    ) {
       date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
    } else {
      // ISO string or Date
      date = new Date(dateInput);
    }
    if (isNaN(date.getTime())) return "Invalid date";
    return format(date, "MMM d, yyyy h:mm a");
  };
