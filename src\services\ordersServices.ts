// services/ordersServices.ts

import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  deleteDoc,
  doc,
  Timestamp,
  serverTimestamp,
  query,
  where,
  documentId,
  orderBy,
  runTransaction,
  increment,
  limit,
  setDoc,
  getCountFromServer,
} from "firebase/firestore";
import { initFirebase } from "../../firebaseConfig";
import {
  getOrderInfoDesc,
  getOrderStatusDesc,
  OrderActivityType,
  OrderInfo,
  OrderInfoName,
  OrderStatusType,
} from "@/lib/constant";
import { getAuth } from "firebase/auth";
import { Service } from "./serviceService";
import { MailServiceManager } from "./MailService";
import { GetUserInfo } from "./usersServices";
import { format } from "date-fns";
import { BASE_URL, GATE_URL, getIdToken } from "@/lib/utils";

// Type definition for Order
export type Order = {
  id?: string;
  comment?: string;
  profileId: string;
  selectedCustomizations?: string[];
  serviceId: string;
  serviceModel?: Service;
  status: string;
  userProfileId: string;
  userProfileName?: string | null;
  added_at?: Timestamp;
  deleted?: boolean;

  // extra
  specificDueDate?: Timestamp;
  dueDate?: Timestamp;
  newDueDate?: Timestamp;

  currency?: string;
  deliveryDetails?: string;

  // more extra

  transactionId?: string;
  uniqueId?: string;
  chargeId?: string;
  payment_intent_id?: string;
  // Track escrow releases per stage
  releasedStages?: Array<{
    stage: "accept" | "delivered" | "completed";
    amount: number; // smallest unit
    transferId: string;
    releasedAt: Timestamp;
  }>;
};
import type { Timestamp as ClientTimestamp } from "firebase/firestore";
import type { Timestamp as AdminTimestamp } from "firebase-admin/firestore";


type AnyTimestamp = ClientTimestamp | AdminTimestamp;

export interface ActivityLog {
  orderId: string;
  date: AnyTimestamp;
  description: string;
  from: "creator" | "user";
  previousStatus?: OrderStatusType;
  title: OrderStatusType;
  type: OrderActivityType;
  newDueDate: Timestamp | undefined;
}
export interface ActivityLogPayload extends ActivityLog {
  orderId: string;
  loggedInUser: string;
  sellerName: string;
  userName: string;
  reason: string;
}

// {
//   id: "0WYZC3RjaUO8HOvF31KE",
//   status: "BASKET",
//   comment: "",
//   userProfileId: "xQOE14rs27dkcpyLN2QctlTZiPj1",
//   serviceId: "UvnqRulJs0WwEIRI25iF",
//   userProfileName: null,
//   profileId: "LJgKQupO0mNUZmXVktZRMBZuGhx1",
//   selectedCustomizations: ["dpxuP9SckZg61KXL9onZ"],
//   added_at: {
//     seconds: 1668276853,
//     nanoseconds: 23999000,
//   },
// },

// Collection name
const ORDERS_COLLECTION = "orders";

/**
 * Create a new order in the Firestore collection.
 * @param orderData - Order data excluding id and added_at.
 * @returns Success status and the newly created document ID or an error message.
 */
export const createOrder = async (orderData: Omit<Order, "id">) => {
  try {
    const newOrder = {
      ...orderData,
      // added_at: Timestamp.now(),
    };
    const { db } = await initFirebase();

    const docRef = await addDoc(collection(db, ORDERS_COLLECTION), newOrder);

    return { success: true, id: docRef.id };
  } catch (error) {
    // console.error("Error creating order:", error);
    return { success: false, error: "Failed to create order" };
  }
};

/**
 * Retrieve all orders from the Firestore collection.
 * @returns Success status and an array of orders or an error message.
 */

// check delete
//not used
export const getAllOrders = async () => {
  try {
    const { db } = await initFirebase();

    const querySnapshot = await getDocs(collection(db, ORDERS_COLLECTION));

    const orders: Order[] = querySnapshot.docs
      .map((doc) => ({
        id: doc.id,
        ...(doc.data() as Omit<Order, "id">),
      }))
      .filter((order: Order) => order.deleted !== true);

    return { success: true, orders };
  } catch (error) {
    // console.error("Error fetching orders:", error);
    return { success: false, error: "Failed to fetch orders" };
  }
};

/**
 * Retrieve a specific order by its ID.
 * @param id - The ID of the order to retrieve.
 * @returns Success status and the order data or an error message.
 */
export const getOrderById = async (id: string) => {
  try {
    console.log("$!@$@!$!@$!$!@$",id);
    
      const idToken = await getIdToken();
      console.log({idToken});
      
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "getOrderById",
              payload: {
                id
              },
            }),
          });
          console.log({response});

          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
            return { success: false, error: "Order not found" };
          }
          let resp = (await response.json())?.message;    
          console.log({resp});
          
          
          return resp
    
    // const { db } = await initFirebase();

    // const docRef = doc(db, ORDERS_COLLECTION, id);
    // const docSnap = await getDoc(docRef);

    // if (docSnap.exists()) {
    //   return {
    //     success: true,
    //     order: { id: docSnap.id, ...docSnap.data() } as Order,
    //   };
    // } else {
    //   return { success: false, error: "Order not found" };
    // }
  } catch (error) {
    console.error("Error fetching order by ID:", error);
    return { success: false, error: "Failed to fetch order" };
  }
};

/**
 * Update an existing order in the Firestore collection.
 * @param id - The ID of the order to update.
 * @param updatedData - Partial data to update the order.
 * @returns Success status or an error message.
 */
export const updateOrder = async (
  id: string,
  updatedData: Partial<Omit<Order, "id" | "added_at">>
) => {
  try {
      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "updateOrder",
              payload: {
                id,
                updatedData
              },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let resp = (await response.json());    
          return resp    
    
    // const { db } = await initFirebase();

    // const docRef = doc(db, ORDERS_COLLECTION, id);
    // await updateDoc(docRef, {
    //   ...updatedData,
    //   added_at: serverTimestamp(),
    // });

    // return { success: true };
  } catch (error) {
    // console.error("Error updating order:", error);
    return { success: false, error: "Failed to update order" };
  }
};

/**
 * Update order status and trigger escrow release if applicable
 * @param id - The ID of the order to update
 * @param newStatus - The new status for the order
 * @param chargeId - Optional Stripe charge ID for escrow releases
 * @returns Success status and escrow release info
 */
/**
 * Release escrow payment for a specific stage
 * @param orderId - The order ID
 * @param stage - The escrow stage ('accept', 'delivered', 'completed')
 * @returns Success status and release details
 */
export const releaseEscrowPayment = async (
  orderId: string,
  stage: "accept" | "delivered" | "completed"
) => {
  try {
    // Get the order to pass details to the release API, but don't strictly require chargeId
    console.log("enter...........");
    if(!orderId?.length){
       return { success: false, error: "Order not found" };
    }
    // const orderResult = await getOrderById(orderId);
    // if (!orderResult.success || !orderResult.order) {
    //   console.error("Order not found for escrow release:", orderId);
    //   return { success: false, error: "Order not found" };
    // }
    

    // const order = orderResult.order;
    // console.log("here" , order);

    // if (!order.chargeId) {
    //   console.error("No chargeId found for order:", orderId);
    //   return { success: false, error: "No charge ID found for this order" };
    // }

    // order?.userProfileId // seller id
    // order.status

    // Call the release escrow API (API can derive chargeId from payment_intent_id if needed)
    
    let url = `${BASE_URL}/api/escrow/release`;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        orderId,
        stage,
        // Only include chargeId if present; the API will derive otherwise
        // ...(order.chargeId ? { chargeId: order.chargeId } : {}),
      }),
    });

    const result = await response.json();
    console.log({result});
    

    if (!response.ok) {
      console.error("Escrow release API error:", result);
      return {
        success: false,
        error: result.error || "Failed to release escrow payment",
      };
    }

    console.log("Escrow payment released successfully:", {
      orderId,
      stage,
      transferId: result.transferId,
      amount: result.amount,
      currency: result.currency,
    });

    return {
      success: true,
      transferId: result.transferId,
      amount: result.amount,
      currency: result.currency,
      stage,
      message: result.message,
    };
  } catch (error) {
    console.error("Error releasing escrow payment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

/**
 * Mark an escrow stage released on an order (idempotent)
 */
export const markEscrowStageReleased = async (
  orderId: string,
  stage: "accept" | "delivered" | "completed",
  amount: number,
  transferId: string
): Promise<{ success: boolean; idempotent?: boolean; error?: string }> => {
  try {

      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "markEscrowStageReleased",
              payload: {
                orderId,
                stage,
                amount,
                transferId
              },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let resp = (await response.json());    
          return resp;    
    // const { db } = await initFirebase();
    // const orderRef = doc(db, ORDERS_COLLECTION, orderId);
    // return await runTransaction(db, async (tx) => {
    //   const snap = await tx.get(orderRef);
    //   if (!snap.exists()) return { success: false, error: "Order not found" };

    //   const data = snap.data() as Order & { releasedStages?: any[] };
    //   const releasedStages = (data.releasedStages || []) as Array<{
    //     stage: string;
    //     transferId: string;
    //   }>;
    //   const already = releasedStages.find((s) => s.stage === stage);
    //   if (already) return { success: true, idempotent: true };

    //   const newEntry = {
    //     stage,
    //     amount,
    //     transferId,
    //     releasedAt: Timestamp.now(),
    //   };

    //   tx.update(orderRef, { releasedStages: [...releasedStages, newEntry] });
      // return { success: true };
    // });
  } catch (e) {
    console.error("markEscrowStageReleased failed:", e);
    return { success: false, error: "Failed to mark stage released" };
  }
};

/**
 * Cancel escrow payment (for declined orders)
 * @param orderId - The order ID
 * @returns Success status and cancellation details
 */
export const cancelEscrowPayment = async (orderId: string) => {
  try {
    console.log("cancelEscrowPayment");

    // Get the order to find payment details
    // const orderResult = await getOrderById(orderId);
    // if (!orderResult.success || !orderResult.order) {
    //   console.error("Order not found for escrow cancellation:", orderId);
    //   return { success: false, error: "Order not found" };
    // }

    // const order = orderResult.order;
    // console.log("order", order);
    // Check if we have either chargeId or transactionId (payment intent ID)
    // if (!order.chargeId && !order.transactionId) {
    //   console.error("No payment details found for order:", orderId);
    //   return { success: false, error: "No payment details found for this order" };
    // }
    let url = `${BASE_URL}/api/stripe/cancel`;

    // Call the cancel escrow API
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        orderId,
        // chargeId: order.chargeId,
        // paymentIntentId: order?.payment_intent_id,
        // transactionId: order.transactionId,
        //also add isUS,currency
      }),
    });

    const result = await response.json();

    console.log(result);

    if (!response.ok) {
      console.error("Escrow cancel API error:", result);
      return {
        success: false,
        error: result.error || "Failed to cancel escrow payment",
      };
    }

    // console.log("Escrow payment cancelled successfully:", {
    //   orderId,
    //   paymentIntentId: order?.payment_intent_id,
    //   transactionId: order.transactionId,
    //   chargeId: order.chargeId,
    // });

    return {
      success: true,
      message: result.message || "Escrow payment cancelled successfully",
    };
  } catch (error) {
    console.error("Error cancelling escrow payment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

export const updateOrderWithEscrow = async (id: string, newStatus: string, charge?: string) => {
  try {
    // First update the order
    const updateResult = await updateOrder(id, { status: newStatus });
    if (!updateResult.success) {
      return updateResult;
    }

    // Check if this status change should trigger an escrow release
    const escrowStatuses = ["accept", "accepted", "delivered", "completed", "complete"];
    if (escrowStatuses.includes(newStatus.toLowerCase()) && charge) {
      try {
        // Call the auto-release API
        const response = await fetch("/api/escrow/auto-release", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            orderId: id,
            newStatus,
            charge,
          }),
        });

        const escrowResult = await response.json();

        return {
          success: true,
          orderUpdated: true,
          escrowReleased: escrowResult.success || false,
          escrowMessage: escrowResult.message,
          escrowDetails: escrowResult.success
            ? {
                stage: escrowResult.stage,
                amount: escrowResult.amount,
                transferId: escrowResult.transferId,
              }
            : null,
        };
      } catch (escrowError) {
        console.error("Error triggering escrow release:", escrowError);
        // Order was updated successfully, but escrow release failed
        return {
          success: true,
          orderUpdated: true,
          escrowReleased: false,
          escrowError: "Failed to trigger escrow release",
        };
      }
    }

    return {
      success: true,
      orderUpdated: true,
      escrowReleased: false,
      message: "Order updated successfully (no escrow release triggered)",
    };
  } catch (error) {
    console.error("Error updating order with escrow:", error);
    return { success: false, error: "Failed to update order" };
  }
};

/**
 * Delete an order from the Firestore collection by its ID.
 * @param id - The ID of the order to delete.
 * @returns Success status or an error message.
 */
export const deleteOrder = async (id: string) => {
  try {
    const { db } = await initFirebase();

    const check = await getOrderById(id);

    if (check.success && check.order?.status === "BASKET") {
      const docRef = doc(db, ORDERS_COLLECTION, id);
      await deleteDoc(docRef);
      return { success: true };
    }
    return { success: false };
  } catch (error) {
    // console.error("Error deleting order:", error);
    return { success: false, error: "Failed to delete order" };
  }
};

////// clean

export const clean = async () => {
  // 86
  // cleaning script
  //   try {
  //     const { db } = await initFirebase();
  //      const ordersRef = collection(db, 'orders');
  //     const snapshot = await getDocs(ordersRef);
  //     const docsToDelete = snapshot.docs
  //       .filter(doc => doc.data().hasOwnProperty('id'))
  //       .map(doc => ({
  //         docId: doc.id,
  //         ...doc.data()
  //       }));
  //       console.log({docsToDelete});
  //  for (const docSnap of docsToDelete) {
  //       await deleteDoc(doc(db, 'orders', docSnap.docId));
  //       console.log(`Deleted doc with ID: ${docSnap.docId}`);
  //     }
  //     console.log("done");
  //     // console.log({orders});
  //   } catch (error) {
  //     console.log({ error });
  //   }
};

export const GetOrderDetailsByUserId = async ({
  userId,
  status,
}: {
  userId: string;
  status?: string;
}) => {
  try {

      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "GetOrderDetailsByUserId",
              payload: {
              userId,
              status,
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let resp = (await response.json())?.message;    
          return resp
    // const { db } = await initFirebase();
    // const ordersRef = collection(db, "orders");
    // const usersRef = collection(db, "users");
    // const servicesRef = collection(db, "services");

    // // Query 1: My Orders (where profileId === userId)
    // const myOrdersQuery = query(
    //   ordersRef,
    //   ...(status ? [where("status", "==", status)] : []),
    //   where("userProfileId", "==", userId),
    //   orderBy("added_at", "desc")
    // );

    // // Query 2: Received Orders (where userProfileId === userId)
    // const receivedOrdersQuery = query(
    //   ordersRef,
    //   ...(status ? [where("status", "==", status)] : []),
    //   where("profileId", "==", userId),
    //   orderBy("added_at", "desc")
    // );

    // const [myOrdersSnapshot, receivedOrdersSnapshot] = await Promise.all([
    //   getDocs(myOrdersQuery),
    //   getDocs(receivedOrdersQuery),
    // ]);

    // // Combine all orders
    // const allOrderDocs = [...myOrdersSnapshot.docs, ...receivedOrdersSnapshot.docs];

    // // Get unique user IDs & service IDs from orders
    // const userIds = new Set<string>();
    // const serviceIds = new Set<string>();

    // allOrderDocs.forEach((doc) => {
    //   const data = doc.data();
    //   userIds.add(data.userProfileId);
    //   userIds.add(data.profileId);
    //   serviceIds.add(data.serviceId);
    // });

    // // Fetch all involved users in one go
    // const userDocs = await Promise.all(
    //   Array.from(userIds).map(async (id) => {
    //     const snap = await getDoc(doc(usersRef, id));
    //     return snap.exists() ? { id, ...snap.data() } : null;
    //   })
    // );

    // // @ts-ignore
    // const userMap = Object.fromEntries(userDocs.filter(Boolean).map((u) => [u.id, u]));
    // // Fetch all services in one go
    // const serviceDocs = await Promise.all(
    //   Array.from(serviceIds).map(async (id) => {
    //     const serviceSnap = await getDoc(doc(servicesRef, id));
    //     if (!serviceSnap.exists()) return null;

    //     // Fetch customizations subcollection
    //     const customizationsSnap = await getDocs(collection(db, "services", id, "customizations"));
    //     const customizations = customizationsSnap.docs
    //       .map((c) => ({
    //         id: c.id,
    //         ...c.data(),
    //         price: parseFloat((c.data().price / (1 - 0.16)).toFixed(2))?.toString(),
    //       }))
    //       ?.filter((curr) => serviceSnap.data()?.customizations?.includes(curr?.id));

    //     let serviceDetail = serviceSnap.data();
    //     serviceDetail.price = parseFloat((serviceDetail.price / (1 - 0.16)).toFixed(2))?.toString();

    //     return {
    //       id,
    //       ...serviceDetail,
    //       customizations,
    //     };
    //   })
    // );
    // // @ts-ignore
    // const serviceMap = Object.fromEntries(serviceDocs.filter(Boolean).map((s) => [s.id, s]));

    // // Helper to fetch activityLog for a single order
    // const fetchActivityLog = async (orderId: string) => {
    //   const activityLogSnap = await getDocs(collection(db, "orders", orderId, "activityLog"));
    //   // Sort by date descending, handle missing/invalid dates
    //   return activityLogSnap.docs
    //     .map((d) => ({ id: d.id, ...(d.data() as ActivityLog) }))
    //     .sort((a, b) => {
    //       const dateA =
    //         a.date && typeof a.date.toDate === "function" ? a.date.toDate().getTime() : 0;
    //       const dateB =
    //         b.date && typeof b.date.toDate === "function" ? b.date.toDate().getTime() : 0;
    //       // Primary sort: date (descending)
    //       if (dateB !== dateA) return dateB - dateA;

    //       // Secondary sort: type priority
    //       const priority = (type: string) => {
    //         if (type === "Order info") return 1;
    //         if (type === "Order status update") return 2;
    //         return 3; // fallback for other types
    //       };

    //       return priority(a.type) - priority(b.type);
    //     });
    // };

    // // Enrich a single order doc
    // const enrichOrder = async (docSnap: (typeof allOrderDocs)[number]) => {
    //   const data = docSnap.data() as Order;
    //   const orderId = docSnap.id;

    //   return {
    //     id: orderId,
    //     ...data,
    //     // @ts-ignore
    //     activityLog: await fetchActivityLog(orderId),
    //     userProfileDetails: userMap[data.userProfileId] || null,
    //     profileDetails: userMap[data.profileId] || null,
    //     serviceDetails: serviceMap[data.serviceId] || null, // edit first give the data?.servicedetail else map one
    //   };
    // };

    // // Enrich all orders in parallel
    // const allOrders = await Promise.all(allOrderDocs.map(enrichOrder));

    // // Separate back into my_orders / received_orders
    // let my_orders = allOrders.filter((o) => o.userProfileId === userId);
    // let received_orders = allOrders.filter((o) => o.profileId === userId);
    // console.log({ received_orders, my_orders });
    // let basket: any = [];

    // if (status === "BASKET") {
    //   my_orders = my_orders.filter((current) => current.status === "BASKET");
    // } else {
    //   let temp_my_orders = my_orders;
    //   my_orders = my_orders.filter((current) => current.status !== "BASKET");
    //   received_orders = received_orders.filter((current) => current.status !== "BASKET");
    //   basket = temp_my_orders.filter((current) => current.status === "BASKET");
    // }
    // //@ts-ignore
    // my_orders = my_orders?.sort((a, b) => b.added_at - a.added_at);

    // //@ts-ignore
    // received_orders = received_orders?.sort((a, b) => b.added_at - a.added_at);

    // //@ts-ignore
    // basket = basket?.sort((a, b) => b.added_at - a.added_at);

    // return {
    //   my_orders,
    //   received_orders,
    //   basket,
    // };
  } catch (error) {
    console.log({ error });
    throw new Error("getOrderDetailsByUserIdFailed");
  }
};

/**
 *  this funciton right now just add to basket
 *  */
export const CreateOrder_V2 = async ({ orderData }: { orderData: Order }) => {
  try {


      const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "CreateOrder_V2",
              payload: {
              orderData
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let resp = (await response.json());    
          return resp
    
    // // console.log("CreateOrder_V2 called with orderData:", orderData);

    // // Validate required fields
    // if (!orderData.userProfileId) {
    //   throw new Error("userProfileId is required");
    // }
    // if (!orderData.profileId) {
    //   throw new Error("profileId is required");
    // }
    // if (!orderData.serviceId) {
    //   throw new Error("serviceId is required");
    // }
    // if (!orderData.status) {
    //   throw new Error("status is required");
    // }

    // const { db } = await initFirebase();

    // // Check for existing order in basket
    // const existingQuery = query(
    //   collection(db, ORDERS_COLLECTION),
    //   where("userProfileId", "==", orderData.userProfileId),
    //   where("profileId", "==", orderData.profileId),
    //   where("serviceId", "==", orderData.serviceId),
    //   where("status", "==", "BASKET")
    // );

    // const existingSnapshot = await getDocs(existingQuery);

    // // if (!existingSnapshot.empty) {
    // //   console.log("Service already exists in basket");
    // //   return {
    // //     success: false,
    // //     error: "Service already exists in basket",
    // //   };
    // // }

    // // Get user data for currency information
    // console.log("Looking for user with serviceId:", orderData?.serviceId);
    // const usersRef = collection(db, "users");
    // const q = query(usersRef, where("serviceIds", "array-contains", orderData?.serviceId));
    // const usersSnap = await getDocs(q);

    // if (usersSnap.empty) {
    //   console.log("No user found with serviceId in serviceIds array, trying alternative approach");

    //   // Alternative: Get user by profileId directly
    //   const userDocRef = doc(db, "users", orderData.profileId);
    //   const userDocSnap = await getDoc(userDocRef);

    //   if (!userDocSnap.exists()) {
    //     console.error("User not found by profileId either:", orderData.profileId);
    //     throw new Error(`No user found with profileId: ${orderData.profileId}`);
    //   }

    //   const userData = userDocSnap.data();
    //   console.log("Found user by profileId:", userData);

    //   const newOrder = {
    //     ...orderData,
    //     added_at: serverTimestamp(),
    //     isUS: userData?.currency?.toLowerCase() === "usd",
    //     currency: userData?.currency || "gbp",
    //   };

    //   const docRef = await addDoc(collection(db, ORDERS_COLLECTION), newOrder);
    //   console.log("Order created successfully with ID:", docRef.id);

    //   return { success: true, id: docRef.id };
    // }

    // const userDoc = usersSnap.docs[0];
    // const userData = userDoc.data();
    // console.log("Found user with serviceIds array:", userData);

    // const newOrder = {
    //   ...orderData,
    //   added_at: serverTimestamp(),
    //   isUS: userData?.currency === "usd" || userData?.currency === "USD",
    //   currency: userData?.currency || "gbp",
    // };

    // const docRef = await addDoc(collection(db, ORDERS_COLLECTION), newOrder);
    // console.log("Order created successfully with ID:", docRef.id);

    // return { success: true, id: docRef.id };
  } catch (error) {
    console.error("CreateOrder_V2 error details:", error);
    console.error("Error stack:", error instanceof Error ? error.stack : "No stack trace");
    throw new Error(
      `CreateOrder_V2_Failed: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// changes
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

function generateNextOrderId(n: number): string {
  return `A${String(n + 1).padStart(6, "0")}`;
}

/**
 *
 * -> it changes state of order from [ BASKET -> NEW ]
 * -> generate order id in ordersIds collection and same is store as uniqueId in orders collection
 *
 */

export const AddtoOrderState = async ({
  id,
  dueDate,
  loggedInUser,
  sellerName,
  userName,
  sendInvoice,
}: {
  id: string;
  dueDate?: Timestamp;
  loggedInUser: string;
  sellerName: string;
  userName: string;
  sendInvoice?: boolean;
}) => {
  try {

         const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "AddtoOrderState",
              payload: {
  id,
  dueDate,
  loggedInUser,
  sellerName,
  userName,
  sendInvoice
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let _resp = (await response.json())?.message;    

    
    
    // const { db } = await initFirebase();

    // const orderRef = doc(db, "orders", id);
    // const orderSnap = await getDoc(orderRef);

    // if (!orderSnap.exists()) {
    //   throw new Error("Order not found");
    // }

    // const order = orderSnap.data() as Order;

    // if (order.status !== "BASKET") {
    //   throw new Error("Order is not in basket state");
    // }

    // const serviceRef = doc(db, "services", order.serviceId);
    // const serviceSnap = await getDoc(serviceRef);

    // if (!serviceSnap.exists()) {
    //   throw new Error("Service not found");
    // }

    // const serviceModel = serviceSnap.data();

    // let customizationsData: any[] = [];
    // if (order.selectedCustomizations?.length) {
    //   const customizationRefs = order.selectedCustomizations.map((cid) =>
    //     doc(db, "services", order.serviceId, "customizations", cid)
    //   );

    //   const customizationSnaps = await Promise.all(customizationRefs.map((ref) => getDoc(ref)));

    //   customizationsData = customizationSnaps
    //     .filter((snap) => snap.exists())
    //     .map((snap) => ({ id: snap.id, ...snap.data() }));
    // }
    // console.log({ serviceModel });

    // serviceModel.servicePrice = serviceModel?.price;
    // serviceModel.price = parseFloat((serviceModel?.price / (1 - 0.16)).toFixed(2))?.toString();

    // serviceModel.customizationsModels = customizationsData?.map((current) => {
    //   return {
    //     ...current,
    //     originalPrice: current.price,
    //     price: parseFloat((current.price / (1 - 0.16)).toFixed(2))?.toString(),
    //   };
    // });

    // //
    // const orderIdsRef = collection(db, "ordersIds");
    // const snapshot = await getDocs(orderIdsRef);
    // const count = snapshot.size;
    // console.log({ count });

    // let newUniqueId = generateNextOrderId(count);
    // //  Create a new document in orderIds collection
    // await setDoc(doc(db, "ordersIds", newUniqueId), {
    //   orderIds: [id],
    // });

    // //
    // let updatePayload = {
    //   status: OrderStatusType.NEW,
    //   uniqueId: newUniqueId,
    //   serviceModel,
    //   dueDate: Timestamp.fromDate(new Date(Date.now() + 48 * 60 * 60 * 1000)),
    //   added_at: Timestamp.now(),
    // };
    // if (dueDate) {
    //   //@ts-ignore
    //   updatePayload.specificDueDate = dueDate;
    // }

    // if (sendInvoice) {
    //   SendInvoiceRequest({ orderId: id });
    // }
    // await updateDoc(orderRef, updatePayload);

    // await UpdateActivityLog({
    //   orderId: orderSnap.id,
    //   description: "",
    //   from: "user",
    //   // newDueDate:, // optional
    //   title: OrderStatusType.NEW,
    //   loggedInUser,
    //   sellerName,
    //   userName,
    //   reason: "asdd",
    // });

    const orderDetails = await getOrderById(id);
    
    // const idToken = await getIdToken();
    const resp = await fetch("/api/stripe/payment-intent/update", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${idToken}`,
      },
      body: JSON.stringify({
            paymentIntentId:orderDetails?.order?.transactionId,
            description:`Payment to Nascent Ventures - Order ${orderDetails?.order?.uniqueId}` , 
            metadata:{
              order_id:orderDetails?.order?.uniqueId
        },
      }),
    });
    if (response.status !== 200) {
      const errorText = await response.text();
      console.log({ errorText });
    }

          return resp

    
  } catch (error) {
    console.log({ error });

    throw new Error("AddtoOrderState_Failed");
  }
};

/**
 *
 * -> update stripe_id(account Id ) of seller
 *
 */
// type :"creator" ask ??? ℹ️
export const UpdateUserAccountId = async ({
  user_id,
  stripe_id,
}: {
  user_id: string;
  stripe_id: string;
}) => {
  try {
   const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "UpdateUserAccountId",
              payload: {
  user_id,
  stripe_id,
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let _resp = (await response.json());    

    // const { db } = await initFirebase();
    // const auth = getAuth();
    // const user = auth.currentUser;

    // if (!user) {
    //   return { success: false, id: null, message: "user not logged in , please login again" };
    // }

    // if (user_id !== user.uid) {
    //   return {
    //     success: false,
    //     message: "unauth",
    //   };
    // }
    // const usersRef = doc(db, "users", user_id);
    // await updateDoc(usersRef, {
    //   stripe_id,
    // });

    return { success: true };
  } catch (error) {
    throw new Error("UpdateUserAccountId_Failed");
  }
};

/**
 *
 * update chargeId,transactionId of order id
 *
 *
 */
export const UpdateOrderStripeDetails = async ({
  id, // order id // document id
  chargeId,
  transactionId,
}: {
  id: string;
  chargeId: string;
  transactionId: string;
}) => {
  try {

       const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "UpdateOrderStripeDetails",
              payload: {
  id, 
  chargeId,
  transactionId,
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let _resp = (await response.json());  
    // const { db } = await initFirebase();

    // const orderRef = doc(db, "orders", id);

    // const resp = await updateDoc(orderRef, {
    //   chargeId,
    //   transactionId,
    // });

    // console.log(`Order ${id} updated with Stripe details`);

    return "ok";
  } catch (error) {
    throw new Error("UpdateOrderStripeDetails_Failed");
  }
};

export const UpdateActivityLog = async ({
  orderId,
  description,
  from,
  newDueDate, // optional
  title,
  loggedInUser,
  sellerName,
  userName,
  reason,
}: Partial<ActivityLogPayload>) => {
  try {

    const idToken = await getIdToken();
      const response = await fetch(GATE_URL, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
               Authorization: `Bearer ${idToken}`,
            },
            body: JSON.stringify({
              type: "UpdateActivityLog",
              payload: {
 orderId,
  description,
  from,
  newDueDate, // optional
  title,
  loggedInUser,
  sellerName,
  userName,
  reason,
            },
            }),
          });
          if (response.status !== 200) {
            const errorText = await response.text();
            console.log({ errorText });
          }
          let _resp = (await response.json());  
    return "ok";
          
    
    // const { db, auth } = await initFirebase();

    // const user = auth.currentUser;
    // console.log({ user });
    // console.log({
    //   orderId,
    //   description,
    //   from,
    //   newDueDate, // optional
    //   title,
    //   loggedInUser,
    //   sellerName,
    //   userName,
    //   reason,
    // });

    // if (!user)
    //   return { success: false, id: null, message: "user not logged in , please login again" };
    // if (!loggedInUser) return { success: false, id: null, message: "logged in user not found" };
    // if (!sellerName) return { success: false, id: null, message: "sellerName  not found" };
    // if (!userName) return { success: false, id: null, message: "userName  not found" };
    // if (!orderId) return { success: false, id: null, message: "orderId  not found" };
    // if (!reason) return { success: false, id: null, message: "reason  not found" };
    // if (!title) return { success: false, id: null, message: "title  not found" };

    // let uniqueOrderId: string | undefined;
    // const docRef = doc(db, "orders", orderId);
    // const docSnap = await getDoc(docRef);
    // if (docSnap.exists()) {
    //   uniqueOrderId = docSnap.data()?.uniqueId;
    // } else {
    //   return { success: false, error: "order not found" };
    // }

    // [TODO]
    // update activity log
    // update orders docs status

    // let activityLogStatusPayload: Partial<ActivityLog> = {
    //   orderId: uniqueOrderId,
    //   title,
    //   date: Timestamp.now(),
    //   type: OrderActivityType.orderStatusUpdate,
    // };

    // let activityLogInfoPayload: Partial<ActivityLog> = {
    //   orderId: uniqueOrderId,
    //   date: Timestamp.now(),
    //   type: OrderActivityType.orderInfo,
    // };

    // let orderInfoStatus: OrderInfo | undefined = undefined;

    // switch (title) {
    //   case OrderStatusType.NEW:
    //     orderInfoStatus = OrderInfo.full;
    //     break;
    //   case OrderStatusType.REFUND_REQUEST:
    //     break;
    //   case OrderStatusType.REFUNDED:
    //     orderInfoStatus = OrderInfo.refund;
    //     break;
    //   case OrderStatusType.CANCELLATION_REQUEST:
    //     break;
    //   case OrderStatusType.CANCELLED:
    //     break;
    //   case OrderStatusType.COMPLETED:
    //     orderInfoStatus = OrderInfo.eighty;
    //     break;
    //   case OrderStatusType.DELIVERED:
    //     orderInfoStatus = OrderInfo.ten;
    //     break;
    //   case OrderStatusType.AUTO_COMPLETED:
    //     orderInfoStatus = OrderInfo.eighty;
    //     break;
    //   case OrderStatusType.INCOMPLETE:
    //     break;
    //   case OrderStatusType.DECLINED:
    //     break;
    //   case OrderStatusType.ACCEPTED:
    //     orderInfoStatus = OrderInfo.ten;
    //     break;
    //   case OrderStatusType.REVISION_REQUEST:
    //     break;
    //   case OrderStatusType.AUTO_DECLINED:
    //     break;
    //   case OrderStatusType.BASKET:
    //     break;
    //   default:
    //     break;
    // }
    // const _activityLogRef = collection(db, "orders", orderId, "activityLog");
    // const checkQ = query(_activityLogRef, where("title", "==", OrderStatusType.REVISION_REQUEST));

    // const revisionSnapshot = await getDocs(checkQ);
    // console.log(`Checking for revision requests for order ${orderId}:`, {
    //   revisionRequestsFound: revisionSnapshot.size,
    //   isEmpty: revisionSnapshot.empty,
    //   currentTitle: title,
    //   orderInfoStatusBeforeCheck: orderInfoStatus,
    // });

    // // Only prevent 10% payments (not 80% completion payments) if there were revision requests
    // if (!revisionSnapshot.empty && orderInfoStatus === OrderInfo.ten) {
    //   // If there are revision requests, don't give 10% payment for ACCEPTED/DELIVERED status
    //   console.log(
    //     `Found ${revisionSnapshot.size} revision requests, preventing 10% payment for status: ${title}`
    //   );
    //   orderInfoStatus = undefined;
    // }
    // // The revision check should only affect 10% payments (ACCEPTED/DELIVERED), not completion payments (COMPLETED)

    // console.log("Final orderInfoStatus:", orderInfoStatus);
    // // status == delivered
    // // check if any prev state was revesion then dont give 10%
    // // orderInfoStatus = undefiend;

    // // order info
    // if (orderInfoStatus) {
    //   let orderInfoPayload = {
    //     loggedInUser,
    //     sellerName,
    //     status: orderInfoStatus,
    //   };
    //   if (newDueDate) {
    //     orderInfoPayload = {
    //       ...orderInfoPayload,
    //       ...{
    //         newDateModel: {
    //           formattedDate: newDueDate,
    //           comment: description ?? "",
    //           reason,
    //           orderId,
    //         },
    //       },
    //     };
    //   }
    //   const orderInfoDesc = getOrderInfoDesc(orderInfoPayload);
    //   activityLogInfoPayload = {
    //     ...activityLogInfoPayload,
    //     title: OrderInfoName[orderInfoStatus],
    //     description: orderInfoDesc,
    //   } as ActivityLog;

    //   console.log({ activityLogInfoPayload });

      //
      // if (orderInfoStatus === OrderInfo.ten) {
      //   // release 10%
      // //   await releaseEscrowPayment(orderId, "accept");
      // // }

      // if (orderInfoStatus === OrderInfo.ten) {
      //   // release 10%
      //   if (title === OrderStatusType.DELIVERED) {
      //     await releaseEscrowPayment(orderId, "delivered");
      //   } else if (title === OrderStatusType.ACCEPTED) {
      //     await releaseEscrowPayment(orderId, "accept");
      //   }
      // }

    //   if (orderInfoStatus === OrderInfo.eighty) {
    //     // release 80%
    //     await releaseEscrowPayment(orderId, "completed");
    //   }

    //   if (orderInfoStatus === OrderInfo.refund) {
    //     // refund
    //   }

    //   if (orderInfoStatus === OrderInfo.full) {
    //     // pre auth
    //   }
    // }

    // //
    // if (title === OrderStatusType.DECLINED) {
    //   // cancel capture auth
    //   await cancelEscrowPayment(orderId);
    // }

    // // order status
    // const orderStatusDesc = getOrderStatusDesc({
    //   profileType: from as "creator" | "user",
    //   sellerName,
    //   status: title,
    //   userName,
    //   comment: description,
    //   reason,
    // });

    // // const _activityLogRef = collection(db, "orders", orderId, "activityLog");
    // const q = query(
    //   _activityLogRef,
    //   where("type", "==", OrderActivityType.orderStatusUpdate),
    //   orderBy("date", "desc"),
    //   limit(1)
    // );
    // let previousStatus: OrderStatusType | undefined;
    // const snapshot = await getDocs(q);
    // const lastLog = snapshot.docs[0];

    // if (lastLog?.exists()) {
    //   const data = lastLog.data();
    //   previousStatus = data?.title;
    // }

    // activityLogStatusPayload = {
    //   ...activityLogStatusPayload,
    //   title,
    //   from,
    //   description: orderStatusDesc,
    // } as ActivityLog;

    // if (previousStatus !== undefined) {
    //   activityLogStatusPayload.previousStatus = previousStatus;
    // }

    // if (newDueDate !== undefined) {
    //   activityLogStatusPayload.newDueDate = newDueDate;
    //   activityLogInfoPayload.newDueDate = newDueDate;
    // }

    // // Step 1: Update order status
    // const orderRef = doc(collection(db, "orders"), orderId);
    // await updateDoc(orderRef, {
    //   status: title,
    // });

    // // update activity logs
    // console.log({ activityLogStatusPayload, activityLogInfoPayload });

    // let activityLogRef = collection(db, "orders", orderId, "activityLog");
    // await Promise.all(
    //   orderInfoStatus
    //     ? [
    //         addDoc(activityLogRef, activityLogStatusPayload),
    //         addDoc(activityLogRef, activityLogInfoPayload),
    //       ]
    //     : [addDoc(activityLogRef, activityLogStatusPayload)]
    // );

    // ///////////////////////
    // if (title === OrderStatusType.ACCEPTED || title === OrderStatusType.DELIVERED) {
    //   await UpdateOrderDueDate(orderId);
    // }

    // send email
    // const orderResult = await getOrderById(orderId);
    // let uniqueId: string = orderResult?.order?.uniqueId ?? "";
    // const seller_mail: string = (await GetUserInfo(orderResult?.order?.profileId ?? ""))?.email;
    // const buyer_mail: string = (await GetUserInfo(orderResult?.order?.userProfileId ?? ""))?.email;

    // if (title === OrderStatusType.ACCEPTED) {
    //   await MailServiceManager.getInstance()?.sendMail({
    //     toMail: activityLogStatusPayload?.from === "user" ? seller_mail : buyer_mail,
    //     type: "order_update",
    //     message: {
    //       orderId: uniqueId,
    //       status: title,
    //       message: `
    //       ${activityLogStatusPayload?.description ?? ""}
    //       Order due date: ${orderResult?.order?.dueDate ? formatDate(orderResult?.order?.dueDate) : ""}
    //       `,
    //     },
    //   });
    // } else {
    //   await MailServiceManager.getInstance()?.sendMail({
    //     toMail: activityLogStatusPayload?.from === "user" ? seller_mail : buyer_mail,
    //     type: "order_update",
    //     message: {
    //       orderId: uniqueId,
    //       status: title,
    //       message: `
    //       ${activityLogStatusPayload?.description ?? ""}   
    //       `,
    //     },
    //   });
    // }

    // return "ok";
  } catch (error) {
    console.log({ error });

    throw new Error("UpdateActivityLog_Failed");
  }
};

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

export function getDueDate(orders: Order[]): Date {
  let workHours = 0;

  for (const order of orders) {
    const serviceDuration = parseInt(order.serviceModel?.duration || "0", 10);
    if (!isNaN(serviceDuration)) {
      workHours += serviceDuration;
    }

    const customizations = order?.serviceModel?.customizationsModels || [];

    for (const customization of customizations) {
      const customizationDuration = parseInt(customization?.duration?.toString() ?? "0");
      if (!isNaN(customizationDuration)) {
        workHours += customizationDuration;
      }
    }
  }

  const totalDays = Math.floor(workHours / 8); // whole days
  const totalHours = workHours % 8;

  const additionalDays = totalDays > 0 ? 1 : 0;

  let dueDateTime = new Date();
  dueDateTime.setDate(dueDateTime.getDate() + totalDays + additionalDays);

  if (totalHours > 0) {
    dueDateTime.setDate(dueDateTime.getDate() + 1);
  }

  return dueDateTime;
}

// only call when order status === "accepted" | "delivered"
export const UpdateOrderDueDate = async (orderId: string) => {
  try {
    const { db } = await initFirebase();

    const docRef = doc(db, "orders", orderId);
    const docSnap = await getDoc(docRef);
    let order: Order | undefined;

    if (docSnap.exists()) {
      order = docSnap.data() as Order;
    } else {
      return { success: false, error: "order not found" };
    }

    const orderRef = doc(collection(db, "orders"), orderId);
    console.log({ orderRef });

    if (order.status === OrderStatusType.ACCEPTED) {
      await updateDoc(orderRef, {
        dueDate: order?.newDueDate || order?.specificDueDate || getDueDate([order]),
      });
    }

    if (order?.status === OrderStatusType.DELIVERED) {
      await updateDoc(orderRef, {
        lastDeliveredDate: Timestamp.now(), // this will help in auth complete step
      });
    }
  } catch (error) {
    console.log({ error });

    throw new Error("UpdateOrderDueDate_Failed");
  }
};

//TODO
// cron
// auto complete -> 80 % funciton
// auto decline -> CANCEL CAPUTRE

export const testOrderDecline = async () => {
  try {
    const { db } = await initFirebase();

    const now = Timestamp.now();
    const twoDaysAgo = Timestamp.fromMillis(now.toMillis() - 60 * 24 * 60 * 60 * 1000);
    console.log({ twoDaysAgo });

    const existingQuery = query(
      collection(db, ORDERS_COLLECTION),
      // where("status", "==", "NEW") ,
      where("added_at", ">=", twoDaysAgo)
    );

    const snapshot = await getDocs(existingQuery);

    // const snapshot = await db
    //   .collection("orders")
    //   .where("status", "==", "NEW")
    //   .where("added_at", "<=", twoDaysAgo)
    //   .get();

    if (snapshot.empty) {
      console.log("No orders to auto-decline.");
      return;
    }
    let orders = [];

    for (const doc of snapshot.docs) {
      const orderData = doc.data();
      const orderId = doc.id;
      console.log(`Auto-declining order: ${orderId}`);
      orders.push(orderId);
      // await deleteDoc(doc.ref);
      // delte doc
    }

    console.log({ orders });
  } catch (error) {
    console.log({ error });

    throw new Error("testOrderDeclineFailed");
  }
};

//optimse to one api later
export const SendInvoiceRequest = async ({ orderId }: { orderId: string }) => {
  const auth = getAuth();
  const user = auth.currentUser;
  const orderResult = await getOrderById(orderId);
  let uniqueId: string = orderResult?.order?.uniqueId ?? "";
  const customer_email: string = (await GetUserInfo(user?.uid ?? ""))?.email;
  await MailServiceManager.getInstance()?.sendMail({
    toMail: customer_email,
    type: "invoice_request",
    message: {
      orderId: uniqueId,
    },
  });
};

export const formatDate = (dateInput: any) => {
  let date: Date;
  if (
    dateInput &&
    typeof dateInput === "object" &&
    "seconds" in dateInput &&
    "nanoseconds" in dateInput
  ) {
    // Firestore Timestamp
    date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
  } else {
    // ISO string or Date
    date = new Date(dateInput);
  }
  if (isNaN(date.getTime())) return "Invalid date";
  return format(date, "MMM d, yyyy");
};
