import { User } from "@/services/UserInterface";
import { GetFiltersInput, userFilterWrapper } from "@/services/usersServices";
import { getFirestore } from "firebase-admin/firestore";
import { FollowerHandlerManager } from "./follow-handlers";

export class UsersHandlerManager {
  private USERS_COLLECTION = "users";

  static instance: UsersHandlerManager | null = null;

  private constructor() {}

  static getInstance() {
    if (!this.instance) {
      this.instance = new UsersHandlerManager();
    }
    return this.instance;
  }
  private _sanitizeUserPayload(data: User) {
    const { stripe_id, email, basketOrdersCount, password, ...safeData } = data;

    return safeData;
  }

  GetAllUsers = async (
    {filters,uid}:{filters?: Omit<GetFiltersInput, "date_of_publishing">,uid?:string}) => {
    try {
      const db = getFirestore();

      const snapshot = await db.collection("users").get();

      let usersList: any[] = snapshot.docs
        .map(
          (doc) =>
            ({
              id: doc.id,
              ...doc.data(),
            }) as User
        )
        .filter((user) => user.isDeleted !== true);

      usersList = usersList?.map((c) => {
        return this._sanitizeUserPayload(c);
      });

      if (filters) {
        usersList = await userFilterWrapper({ filters, users: usersList });
      }
console.log({uid});

  // ✅ Add is_followed_by_me field
    if (uid) {
      const myFollowers = await FollowerHandlerManager.getInstance()?.GetFollowingsByUserId(uid) ;

      const followedIds = myFollowers.map((f: any) =>
        typeof f === "string" ? f : f.id
      );
      console.log({followedIds});
      

      usersList = usersList.map((u) => ({
        ...u,
        is_followed_by_me: followedIds.includes(u.id),
      }));
    } else {
      // if no uid passed, default false
      usersList = usersList.map((u) => ({
        ...u,
        is_followed_by_me: false,
      }));
    }
      
      
      return usersList;
    } catch (error) {
      console.error("Error fetching users:", error);
      return { success: false, error: "Server error" };
    }
  };
}
